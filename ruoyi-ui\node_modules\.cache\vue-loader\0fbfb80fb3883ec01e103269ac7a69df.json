{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\application\\index.vue?vue&type=template&id=7fe2f36b&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\application\\index.vue", "mtime": 1756287010967}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}