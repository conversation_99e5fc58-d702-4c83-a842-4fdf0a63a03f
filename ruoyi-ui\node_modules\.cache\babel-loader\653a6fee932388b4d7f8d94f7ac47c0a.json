{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\index.vue", "mtime": 1756287010975}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_form", "_interopRequireDefault", "require", "_bigForm", "_vuex", "_crud", "components", "Form", "BigForm", "props", "data", "keyword", "expandObj", "query", "list", "listLoading", "formVisible", "formVisible1", "total", "mergeList", "list<PERSON>uery", "currentPage", "pageSize", "computed", "_objectSpread2", "default", "mapGetters", "menuId", "$route", "meta", "modelId", "created", "initSearchDataAndListData", "methods", "addOrUpdateHandle", "id", "isDetail", "is<PERSON><PERSON><PERSON>", "_this", "$nextTick", "$refs", "addForm", "init", "updateHandle", "row", "_this2", "arraySpanMethod", "_ref", "column", "i", "length", "property", "prop", "rowspan", "colspan", "sortChange", "_ref2", "order", "initData", "_this3", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "initSearchData", "stop", "_callee2", "_callee2$", "_context2", "_this4", "_query", "getList", "then", "res", "_list", "_data", "push", "map", "o", "pagination", "search", "sort", "sidx", "refresh", "isrRefresh", "reset", "key", "undefined", "handleDel", "_this5", "$confirm", "type", "delData", "$message", "message", "msg", "catch"], "sources": ["src/views/aqsoc/hw-work/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"custom-container\">\r\n    <div v-show=\"!formVisible1\" class=\"custom-content-container-right\">\r\n      <div class=\"custom-content-search-box\">\r\n        <el-row :gutter=\"16\">\r\n          <el-form @submit.native.prevent>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"年度\">\r\n                <el-input v-model=\"query.year\" placeholder=\"请输入年度\" clearable>\r\n                </el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"技术支撑单位\">\r\n                <el-input v-model=\"query.supportOrgs\" placeholder=\"请输入技术支撑单位\" clearable>\r\n                </el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item class=\"custom-search-btn\">\r\n                <el-button class=\"btn1\" size=\"small\" @click=\"search()\">查询</el-button>\r\n                <el-button class=\"btn2\" size=\"small\" @click=\"reset()\">重置</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-form>\r\n        </el-row>\r\n      </div>\r\n\r\n      <div class=\"custom-content-container\">\r\n        <div class=\"common-header\">\r\n          <div><span class=\"common-head-title\">HW事务列表</span></div>\r\n          <div class=\"common-head-right\">\r\n            <el-row :gutter=\"10\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"small\"\r\n                  @click=\"addOrUpdateHandle()\"\r\n                >新增\r\n                </el-button >\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </div>\r\n        <el-table height=\"100%\" v-loading=\"listLoading\" :data=\"list\" @sort-change='sortChange'\r\n                  :span-method=\"arraySpanMethod\">\r\n          <el-table-column\r\n            prop=\"year\"\r\n            label=\"年度\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"supportOrgs\"\r\n            label=\"技术支撑单位\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"userNames\"\r\n            label=\"联络人\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"hwStart\"\r\n            label=\"HW开始时间\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"hwEnd\"\r\n            label=\"HW结束时间\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"createTime\"\r\n            label=\"创建时间\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" @click=\"updateHandle(scope.row, true)\">HW工作台</el-button>\r\n              <el-button type=\"text\" class=\"table-delBtn\" @click=\"handleDel(scope.row.id)\">删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination :total=\"total\" :page.sync=\"listQuery.currentPage\" :limit.sync=\"listQuery.pageSize\"\r\n                    @pagination=\"initData\"></pagination>\r\n      </div>\r\n      <Form :visible.sync=\"formVisible\" ref=\"addForm\" @refresh=\"refresh\"></Form>\r\n    </div>\r\n    <big-form :visible.sync=\"formVisible1\" ref=\"BigForm\" @refresh=\"refresh\" ></big-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Form from './form'\r\nimport BigForm from './big-form'\r\nimport {mapGetters} from \"vuex\";\r\nimport {getList, delData} from '@/api/aqsoc/work-hw/crud'\r\n\r\nexport default {\r\n  components: {Form, BigForm},\r\n  props: {},\r\n  data() {\r\n    return {\r\n      keyword: '',\r\n      expandObj: {},\r\n      query: {},\r\n      list: [],\r\n      listLoading: true,\r\n      formVisible: false,\r\n      formVisible1: false,\r\n      total: 0,\r\n      mergeList: [],\r\n      listQuery: {\r\n        currentPage: 1,\r\n        pageSize: 20\r\n      },\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters(['userInfo']),\r\n    menuId() {\r\n      return this.$route.meta.modelId || ''\r\n    }\r\n  },\r\n  created() {\r\n    this.initSearchDataAndListData()\r\n  },\r\n  methods: {\r\n    addOrUpdateHandle(id, isDetail, isAudit) {\r\n      this.formVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.addForm.init(id, isDetail, isAudit)\r\n      })\r\n    },\r\n    updateHandle(row, isDetail, isAudit) {\r\n      this.formVisible1 = true\r\n      this.$nextTick(() => {\r\n        this.$refs.BigForm.init(row, isDetail, isAudit)\r\n      })\r\n    },\r\n    arraySpanMethod({column}) {\r\n      for (let i = 0; i < this.mergeList.length; i++) {\r\n        if (column.property == this.mergeList[i].prop) {\r\n          return [this.mergeList[i].rowspan, this.mergeList[i].colspan]\r\n        }\r\n      }\r\n    },\r\n    sortChange({column, prop, order}) {\r\n      this.initData()\r\n    },\r\n    async initSearchDataAndListData() {\r\n      await this.initSearchData()\r\n      this.initData()\r\n    },\r\n    //初始化查询的默认数据\r\n    async initSearchData() {\r\n    },\r\n    initData() {\r\n      this.listLoading = true;\r\n      let _query = {\r\n        ...this.listQuery,\r\n        ...this.query,\r\n        keyword: this.keyword,\r\n        menuId: this.menuId\r\n      };\r\n      getList(_query).then(res => {\r\n        var _list = [];\r\n        for (let i = 0; i < res.data.list.length; i++) {\r\n          let _data = res.data.list[i];\r\n          _list.push(_data)\r\n        }\r\n        this.list = _list.map(o => ({\r\n          ...o,\r\n          ...this.expandObj,\r\n        }))\r\n        this.total = res.data.pagination.total\r\n        this.listLoading = false\r\n      })\r\n    },\r\n    search() {\r\n      this.listQuery.currentPage = 1\r\n      this.listQuery.pageSize = 20\r\n      this.listQuery.sort = \"desc\"\r\n      this.listQuery.sidx = \"\"\r\n      this.initData()\r\n    },\r\n    refresh(isrRefresh) {\r\n      this.formVisible = false\r\n      this.formVisible1 = false\r\n      if (isrRefresh) this.reset()\r\n    },\r\n    reset() {\r\n      for (let key in this.query) {\r\n        this.query[key] = undefined\r\n      }\r\n      this.search()\r\n    },\r\n    handleDel(id) {\r\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delData(id).then(res => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: res.msg,\r\n          });\r\n          this.initData()\r\n        })\r\n      }).catch(() => {\r\n      });\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AAwFA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,UAAA;IAAAC,IAAA,EAAAA,aAAA;IAAAC,OAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,KAAA;MACAC,IAAA;MACAC,WAAA;MACAC,WAAA;MACAC,YAAA;MACAC,KAAA;MACAC,SAAA;MACAC,SAAA;QACAC,WAAA;QACAC,QAAA;MACA;IACA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA;IACAC,MAAA,WAAAA,OAAA;MACA,YAAAC,MAAA,CAAAC,IAAA,CAAAC,OAAA;IACA;EAAA,EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,yBAAA;EACA;EACAC,OAAA;IACAC,iBAAA,WAAAA,kBAAAC,EAAA,EAAAC,QAAA,EAAAC,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAtB,WAAA;MACA,KAAAuB,SAAA;QACAD,KAAA,CAAAE,KAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,EAAA,EAAAC,QAAA,EAAAC,OAAA;MACA;IACA;IACAM,YAAA,WAAAA,aAAAC,GAAA,EAAAR,QAAA,EAAAC,OAAA;MAAA,IAAAQ,MAAA;MACA,KAAA5B,YAAA;MACA,KAAAsB,SAAA;QACAM,MAAA,CAAAL,KAAA,CAAAhC,OAAA,CAAAkC,IAAA,CAAAE,GAAA,EAAAR,QAAA,EAAAC,OAAA;MACA;IACA;IACAS,eAAA,WAAAA,gBAAAC,IAAA;MAAA,IAAAC,MAAA,GAAAD,IAAA,CAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,QAAA9B,SAAA,CAAA+B,MAAA,EAAAD,CAAA;QACA,IAAAD,MAAA,CAAAG,QAAA,SAAAhC,SAAA,CAAA8B,CAAA,EAAAG,IAAA;UACA,aAAAjC,SAAA,CAAA8B,CAAA,EAAAI,OAAA,OAAAlC,SAAA,CAAA8B,CAAA,EAAAK,OAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,KAAA;MAAA,IAAAR,MAAA,GAAAQ,KAAA,CAAAR,MAAA;QAAAI,IAAA,GAAAI,KAAA,CAAAJ,IAAA;QAAAK,KAAA,GAAAD,KAAA,CAAAC,KAAA;MACA,KAAAC,QAAA;IACA;IACA1B,yBAAA,WAAAA,0BAAA;MAAA,IAAA2B,MAAA;MAAA,WAAAC,kBAAA,CAAAnC,OAAA,mBAAAoC,oBAAA,CAAApC,OAAA,IAAAqC,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAApC,OAAA,IAAAuC,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAT,MAAA,CAAAU,cAAA;YAAA;cACAV,MAAA,CAAAD,QAAA;YAAA;YAAA;cAAA,OAAAQ,QAAA,CAAAI,IAAA;UAAA;QAAA,GAAAP,OAAA;MAAA;IACA;IACA;IACAM,cAAA,WAAAA,eAAA;MAAA,WAAAT,kBAAA,CAAAnC,OAAA,mBAAAoC,oBAAA,CAAApC,OAAA,IAAAqC,IAAA,UAAAS,SAAA;QAAA,WAAAV,oBAAA,CAAApC,OAAA,IAAAuC,IAAA,UAAAQ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAN,IAAA,GAAAM,SAAA,CAAAL,IAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAH,IAAA;UAAA;QAAA,GAAAC,QAAA;MAAA;IACA;IACAb,QAAA,WAAAA,SAAA;MAAA,IAAAgB,MAAA;MACA,KAAA3D,WAAA;MACA,IAAA4D,MAAA,OAAAnD,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,KAAAL,SAAA,GACA,KAAAP,KAAA;QACAF,OAAA,OAAAA,OAAA;QACAgB,MAAA,OAAAA;MAAA,EACA;MACA,IAAAiD,aAAA,EAAAD,MAAA,EAAAE,IAAA,WAAAC,GAAA;QACA,IAAAC,KAAA;QACA,SAAA9B,CAAA,MAAAA,CAAA,GAAA6B,GAAA,CAAApE,IAAA,CAAAI,IAAA,CAAAoC,MAAA,EAAAD,CAAA;UACA,IAAA+B,KAAA,GAAAF,GAAA,CAAApE,IAAA,CAAAI,IAAA,CAAAmC,CAAA;UACA8B,KAAA,CAAAE,IAAA,CAAAD,KAAA;QACA;QACAN,MAAA,CAAA5D,IAAA,GAAAiE,KAAA,CAAAG,GAAA,WAAAC,CAAA;UAAA,WAAA3D,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA0D,CAAA,GACAT,MAAA,CAAA9D,SAAA;QAAA,CACA;QACA8D,MAAA,CAAAxD,KAAA,GAAA4D,GAAA,CAAApE,IAAA,CAAA0E,UAAA,CAAAlE,KAAA;QACAwD,MAAA,CAAA3D,WAAA;MACA;IACA;IACAsE,MAAA,WAAAA,OAAA;MACA,KAAAjE,SAAA,CAAAC,WAAA;MACA,KAAAD,SAAA,CAAAE,QAAA;MACA,KAAAF,SAAA,CAAAkE,IAAA;MACA,KAAAlE,SAAA,CAAAmE,IAAA;MACA,KAAA7B,QAAA;IACA;IACA8B,OAAA,WAAAA,QAAAC,UAAA;MACA,KAAAzE,WAAA;MACA,KAAAC,YAAA;MACA,IAAAwE,UAAA,OAAAC,KAAA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,SAAAC,GAAA,SAAA9E,KAAA;QACA,KAAAA,KAAA,CAAA8E,GAAA,IAAAC,SAAA;MACA;MACA,KAAAP,MAAA;IACA;IACAQ,SAAA,WAAAA,UAAA1D,EAAA;MAAA,IAAA2D,MAAA;MACA,KAAAC,QAAA;QACAC,IAAA;MACA,GAAAnB,IAAA;QACA,IAAAoB,aAAA,EAAA9D,EAAA,EAAA0C,IAAA,WAAAC,GAAA;UACAgB,MAAA,CAAAI,QAAA;YACAF,IAAA;YACAG,OAAA,EAAArB,GAAA,CAAAsB;UACA;UACAN,MAAA,CAAApC,QAAA;QACA;MACA,GAAA2C,KAAA,cACA;IACA;EACA;AACA", "ignoreList": []}]}