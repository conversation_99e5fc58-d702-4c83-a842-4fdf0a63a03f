{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\aqsoc\\work-hw\\workHwTask.js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\aqsoc\\work-hw\\workHwTask.js", "mtime": 1756264471501}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1751956515056}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi93c2gvYXVnbWVudF93b3Jrc3BhY2UvYXFzb2MtbWFpbi9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkV29ya0h3VGFzayA9IGFkZFdvcmtId1Rhc2s7CmV4cG9ydHMuZGVsV29ya0h3VGFzayA9IGRlbFdvcmtId1Rhc2s7CmV4cG9ydHMuZ2V0U3RhZ2VUcmVlID0gZ2V0U3RhZ2VUcmVlOwpleHBvcnRzLmdldFdvcmtId1Rhc2sgPSBnZXRXb3JrSHdUYXNrOwpleHBvcnRzLmxpc3RXb3JrSHdUYXNrID0gbGlzdFdvcmtId1Rhc2s7CmV4cG9ydHMudXBkYXRlV29ya0h3VGFzayA9IHVwZGF0ZVdvcmtId1Rhc2s7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6JIV+S6i+WKoeS7u+WKoeWIl+ihqApmdW5jdGlvbiBsaXN0V29ya0h3VGFzayhxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3dvcmtId1Rhc2svbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6JIV+S6i+WKoeS7u+WKoeivpue7hgpmdW5jdGlvbiBnZXRXb3JrSHdUYXNrKGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvd29ya0h3VGFzay8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinkhX5LqL5Yqh5Lu75YqhCmZ1bmN0aW9uIGFkZFdvcmtId1Rhc2soZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3dvcmtId1Rhc2snLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUuUhX5LqL5Yqh5Lu75YqhCmZ1bmN0aW9uIHVwZGF0ZVdvcmtId1Rhc2soZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3dvcmtId1Rhc2snLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6ZmkSFfkuovliqHku7vliqEKZnVuY3Rpb24gZGVsV29ya0h3VGFzayhpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3dvcmtId1Rhc2svJyArIGlkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9CgovLyDmn6Xor6LpmLbmrrXmoJEKZnVuY3Rpb24gZ2V0U3RhZ2VUcmVlKHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvd29ya0h3VGFzay9nZXRTdGFnZVRyZWUnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listWorkHwTask", "query", "request", "url", "method", "params", "getWorkHwTask", "id", "addWorkHwTask", "data", "updateWorkHwTask", "delWorkHwTask", "getStageTree"], "sources": ["E:/wsh/augment_workspace/aqsoc-main/ruoyi-ui/src/api/aqsoc/work-hw/workHwTask.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询HW事务任务列表\nexport function listWorkHwTask(query) {\n  return request({\n    url: '/workHwTask/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询HW事务任务详细\nexport function getWorkHwTask(id) {\n  return request({\n    url: '/workHwTask/' + id,\n    method: 'get'\n  })\n}\n\n// 新增HW事务任务\nexport function addWorkHwTask(data) {\n  return request({\n    url: '/workHwTask',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改HW事务任务\nexport function updateWorkHwTask(data) {\n  return request({\n    url: '/workHwTask',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除HW事务任务\nexport function delWorkHwTask(id) {\n  return request({\n    url: '/workHwTask/' + id,\n    method: 'delete'\n  })\n}\n\n// 查询阶段树\nexport function getStageTree(query) {\n  return request({\n    url: '/workHwTask/getStageTree',\n    method: 'get',\n    params: query\n  })\n}\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc,GAAGI,EAAE;IACxBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACD,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,aAAaA,CAACJ,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc,GAAGI,EAAE;IACxBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACX,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}