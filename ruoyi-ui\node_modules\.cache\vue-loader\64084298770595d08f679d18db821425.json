{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\system\\user\\index.vue?vue&type=style&index=0&id=1930a3c4&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\system\\user\\index.vue", "mtime": 1756287010987}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgo6OnYtZGVlcCAuZWwtdHJlZSB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMjY1cHgpOwogIG92ZXJmbG93LXk6IGF1dG87Cn0KOjp2LWRlZXAgLmVsLXRyZWUtbm9kZV9fY29udGVudHsKICBoZWlnaHQ6IDQ4cHg7CiAgZm9udC1zaXplOiAxNHB4Cn0KOjp2LWRlZXAgLmVsLXRyZWUtbm9kZV9fY29udGVudDpob3ZlcnsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1Owp9Cjo6di1kZWVwIC5lbC10cmVlLS1oaWdobGlnaHQtY3VycmVudCAuZWwtdHJlZS1ub2RlLmlzLWN1cnJlbnQgPiAuZWwtdHJlZS1ub2RlX19jb250ZW50ewogIGxpbmUtaGVpZ2h0OiA0OHB4OwogIGNvbG9yOiAjMzMzMzMzOwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user", "sourcesContent": ["<template>\n  <div :class=\"{'custom-container':!disabled}\">\n    <div class=\"custom-tree-container\">\n      <div class=\"head-container\">\n        <el-input\n          v-model=\"deptName\"\n          placeholder=\"请输入部门名称\"\n          clearable\n          size=\"medium\"\n          prefix-icon=\"el-icon-search\"\n          style=\"margin-bottom: 15px\"\n        />\n      </div>\n      <div class=\"head-container\">\n        <el-tree\n          :data=\"deptOptions\"\n          :props=\"defaultProps\"\n          :expand-on-click-node=\"false\"\n          :filter-node-method=\"filterNode\"\n          node-key=\"id\"\n          ref=\"tree\"\n          default-expand-all\n          highlight-current\n          @node-click=\"handleNodeClick\"\n        />\n      </div>\n    </div>\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          v-show=\"showSearch\"\n          label-position=\"right\"\n          label-width=\"100px\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"账号/昵称\" prop=\"nameSearchKey\">\n                <el-input\n                  v-model=\"queryParams.nameSearchKey\"\n                  placeholder=\"请输入账号或昵称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n                <el-input\n                  v-model=\"queryParams.phonenumber\"\n                  placeholder=\"请输入手机号码\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"状态\" prop=\"status\">\n                <el-select\n                  v-model=\"queryParams.status\"\n                  placeholder=\"用户状态\"\n                  clearable\n                >\n                  <el-option\n                    v-for=\"dict in dict.type.sys_normal_disable\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"创建时间\">\n                <el-date-picker\n                  v-model=\"dateRange\"\n                  value-format=\"yyyy-MM-dd\"\n                  type=\"daterange\"\n                  range-separator=\"-\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                ></el-date-picker>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">用户管理列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\" v-if=\"!disabled\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleAdd\"\n                  v-hasPermi=\"['system:user:add']\"\n                >新增</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  size=\"small\"\n                  class=\"btn1\"\n                  :disabled=\"multiple\"\n                  @click=\"handleDelete\"\n                  v-hasPermi=\"['system:user:remove']\"\n                >批量删除</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  size=\"small\"\n                  class=\"btn1\"\n                  @click=\"handleImport\"\n                  v-hasPermi=\"['system:user:import']\"\n                >导入</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                  v-hasPermi=\"['system:user:export']\"\n                >导出</el-button>\n              </el-col>\n<!--              <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\" :columns=\"columns\"></right-toolbar>-->\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          v-loading=\"loading\"\n          :data=\"userList\"\n          ref=\"table\"\n          height=\"100%\"\n          @selection-change=\"handleSelectionChange\"\n          @select=\"select\" @select-all=\"selectAll\">\n          <el-table-column type=\"selection\" width=\"50\"  />\n<!--          <el-table-column label=\"用户编号\"  key=\"userId\" prop=\"userId\" v-if=\"columns[0].visible\" />-->\n          <el-table-column label=\"账号\"  key=\"userName\" prop=\"userName\" v-if=\"columns[1].visible\" :show-overflow-tooltip=\"true\" />\n          <el-table-column label=\"用户姓名\"  key=\"nickName\" prop=\"nickName\" v-if=\"columns[2].visible\" :show-overflow-tooltip=\"true\" />\n          <el-table-column label=\"部门\"  key=\"deptName\" prop=\"dept.deptName\" v-if=\"columns[3].visible\" :show-overflow-tooltip=\"true\" />\n          <el-table-column label=\"手机号码\"  key=\"phonenumber\" prop=\"phonenumber\" v-if=\"columns[4].visible\" width=\"120\" />\n          <el-table-column label=\"状态\"  key=\"status\" v-if=\"columns[5].visible\">\n            <template slot-scope=\"scope\">\n              <el-switch\n                v-if=\"!disabled\"\n                v-model=\"scope.row.status\"\n                active-value=\"0\"\n                inactive-value=\"1\"\n                @change=\"handleStatusChange(scope.row)\"\n              ></el-switch>\n              <el-tag v-else-if=\"scope.row.status\" type=\"success\">启用</el-tag>\n              <el-tag v-else type=\"danger\">停用</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"创建时间\"  prop=\"createTime\" v-if=\"columns[6].visible\" width=\"160\">\n            <template slot-scope=\"scope\">\n              <span>{{ parseTime(scope.row.createTime) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column\n            v-if=\"!disabled\"\n            label=\"操作\"\n            fixed=\"right\"\n            :show-overflow-tooltip=\"false\"\n            width=\"160\"\n            class-name=\"small-padding fixed-width\"\n          >\n            <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['system:user:edit']\"\n              >编辑</el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                class=\"table-delBtn\"\n                @click=\"handleDelete(scope.row)\"\n                v-hasPermi=\"['system:user:remove']\"\n              >删除</el-button>\n              <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['system:user:resetPwd', 'system:user:edit']\">\n                <span class=\"el-dropdown-link\">\n                  <i class=\"el-icon-d-arrow-right el-icon--right\"></i>更多\n                </span>\n                <el-dropdown-menu slot=\"dropdown\">\n                  <el-dropdown-item command=\"handleResetPwd\" icon=\"el-icon-key\"\n                                    v-hasPermi=\"['system:user:resetPwd']\">重置密码</el-dropdown-item>\n                  <el-dropdown-item command=\"handleAuthRole\" icon=\"el-icon-circle-check\"\n                                    v-hasPermi=\"['system:user:edit']\">分配角色</el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <!-- 添加或修改用户配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item v-if=\"form.userId !== undefined\" label=\"账号\" prop=\"userName\">\n              <el-input v-model=\"form.userName\" placeholder=\"\" maxlength=\"30\" readonly :disabled=\"true\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户姓名\" prop=\"nickName\">\n              <el-input v-model=\"form.nickName\" placeholder=\"请输入用户姓名\" maxlength=\"30\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"归属部门\" prop=\"deptId\">\n              <treeselect v-model=\"form.deptId\" :options=\"deptOptions\" :show-count=\"true\" placeholder=\"请选择归属部门\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n              <el-input v-model=\"form.phonenumber\" placeholder=\"请输入手机号码\" maxlength=\"11\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"邮箱\" prop=\"email\">\n              <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.userId == undefined\" label=\"账号\" prop=\"userName\">\n              <el-input v-model=\"form.userName\" placeholder=\"请输入账号\" maxlength=\"30\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.userId == undefined\" label=\"用户密码\" prop=\"password\">\n              <el-input v-model=\"form.password\" placeholder=\"请输入用户密码\" type=\"password\" maxlength=\"20\" show-password/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户性别\">\n              <el-select v-model=\"form.sex\" placeholder=\"请选择性别\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_user_sex\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"岗位\">\n              <el-select v-model=\"form.postIds\" multiple placeholder=\"请选择岗位\">\n                <el-option\n                  v-for=\"item in postOptions\"\n                  :key=\"item.postId\"\n                  :label=\"item.postName\"\n                  :value=\"item.postId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"角色\">\n              <el-select v-model=\"form.roleIds\" multiple placeholder=\"请选择角色\">\n                <el-option\n                  v-for=\"item in roleOptions\"\n                  :key=\"item.roleId\"\n                  :label=\"item.roleName\"\n                  :value=\"item.roleId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 用户导入对话框 -->\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".xlsx, .xls\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\n        :disabled=\"upload.isUploading\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\n          <div class=\"el-upload__tip\" slot=\"tip\">\n            <el-checkbox v-model=\"upload.updateSupport\" /> 是否更新已经存在的用户数据\n          </div>\n          <span>仅允许导入xls、xlsx格式文件。</span>\n          <el-link type=\"primary\" :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" @click=\"importTemplate\">下载模板</el-link>\n        </div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus, deptTreeSelect } from \"@/api/system/user\";\nimport { getToken } from \"@/utils/auth\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\n\nexport default {\n  name: \"User\",\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\n  components: { Treeselect },\n  props:{\n    disabled:false,\n  },\n  data() {\n    return {\n      showAll: false,\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 用户表格数据\n      userList: null,\n      // 弹出层标题\n      title: \"\",\n      // 部门树选项\n      deptOptions: undefined,\n      // 是否显示弹出层\n      open: false,\n      // 部门名称\n      deptName: undefined,\n      // 默认密码\n      initPassword: undefined,\n      // 日期范围\n      dateRange: [],\n      // 岗位选项\n      postOptions: [],\n      // 角色选项\n      roleOptions: [],\n      // 表单参数\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"label\"\n      },\n      // 用户导入参数\n      upload: {\n        // 是否显示弹出层（用户导入）\n        open: false,\n        // 弹出层标题（用户导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的用户数据\n        updateSupport: 0,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/system/user/importData\"\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userName: undefined,\n        phonenumber: undefined,\n        status: undefined,\n        deptId: undefined,\n        nameSearchKey: null,\n      },\n      // 列信息\n      columns: [\n        { key: 0, label: `用户编号`, visible: true },\n        { key: 1, label: `账号`, visible: true },\n        { key: 2, label: `用户姓名`, visible: true },\n        { key: 3, label: `部门`, visible: true },\n        { key: 4, label: `手机号码`, visible: true },\n        { key: 5, label: `状态`, visible: true },\n        { key: 6, label: `创建时间`, visible: true }\n      ],\n      // 表单校验\n      rules: {\n        userName: [\n          { required: true, message: \"账号不能为空\", trigger: \"blur\" },\n          { min: 2, max: 20, message: '账号长度必须介于 2 和 20 之间', trigger: 'blur' }\n        ],\n        deptId: [\n          { required: true, message: \"部门不能为空\", trigger: \"blur\" },\n        ],\n        nickName: [\n          { required: true, message: \"用户姓名不能为空\", trigger: \"blur\" }\n        ],\n        password: [\n          { required: true, message: \"用户密码不能为空\", trigger: \"blur\" },\n          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }\n        ],\n        email: [\n        {\n            required: false,\n            message: \"邮箱地址不能为空\",\n            trigger: [\"blur\", \"change\"]\n          },\n          {\n            type: \"email\",\n            message: \"请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"]\n          }\n        ],\n        phonenumber: [\n          {\n            required: false,\n            message: \"手机号码不能为空\",\n            trigger: \"blur\"\n          },\n          {\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    };\n  },\n  watch: {\n    // 根据名称筛选部门树\n    deptName(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    this.getList();\n    this.getDeptTree();\n    // if(!this.disabled)\n      // this.getConfigKey(\"sys.user.initPassword\").then(response => {\n      //   this.initPassword = response.msg;\n      // });\n  },\n\n  methods: {\n    getCheckedNodes(){\n      if(this.disabled){\n        let currentNode = this.$refs.tree.getCurrentNode();\n        this.$emit(\"deptSelect\",currentNode);\n        this.$refs.table.clearSelection();\n      }\n    },\n    setCheckedNodes(key,row){\n      if(key) this.$refs.tree.setCurrentKey(key);\n      if(row) this.$refs.table.toggleRowSelection(row,true);\n    },\n    /** 查询用户列表 */\n    getList() {\n      this.loading = true;\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.userList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n          if(this.disabled){\n            this.$emit(\"dataDown\",this.userList,this.queryParams)\n          }\n        }\n      );\n    },\n\n    /** 查询部门下拉树结构 */\n    getDeptTree() {\n      deptTreeSelect().then(response => {\n        this.deptOptions = response.data;\n        if(this.disabled){\n          this.$emit(\"deptSelect\",this.deptOptions[0])\n        }\n      });\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    // 节点单击事件\n    handleNodeClick(data) {\n      if(this.disabled){\n        this.$emit(\"deptSelect\",data)\n      }\n      this.queryParams.deptId = data.id;\n      this.handleQuery();\n\n    },\n    // 用户状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.userName + '\"用户吗？').then(function() {\n        return changeUserStatus(row.userId, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function() {\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        deptId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        password: undefined,\n        phonenumber: undefined,\n        email: undefined,\n        sex: undefined,\n        status: \"0\",\n        remark: undefined,\n        postIds: [],\n        roleIds: []\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      if(!this.disabled){\n        this.ids = selection.map(item => item.userId);\n        this.single = selection.length != 1;\n        this.multiple = !selection.length;\n      }\n    },\n    select(selection,row){\n      if(this.disabled){\n        this.$refs.table.clearSelection();\n        if(!selection.length){\n          this.$emit(\"userSelect\",null)\n        }else {\n          this.$refs.table.toggleRowSelection(row,true)\n          this.$emit(\"userSelect\",row)\n        }\n      }\n    },\n    selectAll(){\n      if(this.disabled)\n        this.$refs.table.clearSelection();\n    },\n    // 更多操作触发\n    handleCommand(command, row) {\n      switch (command) {\n        case \"handleResetPwd\":\n          this.handleResetPwd(row);\n          break;\n        case \"handleAuthRole\":\n          this.handleAuthRole(row);\n          break;\n        default:\n          break;\n      }\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      getUser().then(response => {\n        this.postOptions = response.posts;\n        this.roleOptions = response.roles;\n        this.open = true;\n        this.title = \"添加用户\";\n        this.form.password = this.initPassword;\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const userId = row.userId || this.ids;\n      getUser(userId).then(response => {\n        this.form = response.data;\n        this.postOptions = response.posts;\n        this.roleOptions = response.roles;\n        this.form.postIds = response.postIds;\n        this.form.roleIds = response.roleIds;\n        this.open = true;\n        this.title = \"修改用户\";\n        this.form.password = \"\";\n      });\n    },\n    /** 重置密码按钮操作 */\n    handleResetPwd(row) {\n      this.$prompt('请输入\"' + row.userName + '\"的新密码', \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        closeOnClickModal: false,\n        inputPattern: /^.{5,20}$/,\n        inputErrorMessage: \"用户密码长度必须介于 5 和 20 之间\"\n      }).then(({ value }) => {\n          resetUserPwd(row.userId, value).then(response => {\n            this.$modal.msgSuccess(\"修改成功，新密码是：\" + value);\n          });\n        }).catch(() => {});\n    },\n    /** 分配角色操作 */\n    handleAuthRole: function(row) {\n      const userId = row.userId;\n      this.$router.push(\"/system/user-auth/role/\" + userId);\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.userId != undefined) {\n            updateUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const userIds = row.userId || this.ids;\n      this.$modal.confirm('是否确认删除用户编号为\"' + userIds + '\"的数据项？').then(function() {\n        return delUser(userIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('system/user/export', {\n        ...this.queryParams\n      }, `user_${new Date().getTime()}.xlsx`)\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"用户导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      this.download('system/user/importTemplate', {\n      }, `user_template_${new Date().getTime()}.xlsx`)\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(\"<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>\" + response.msg + \"</div>\", \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    }\n  }\n};\n</script>\n<style lang=\"scss\" scoped>\n::v-deep .el-tree {\n  width: 100%;\n  height: calc(100vh - 265px);\n  overflow-y: auto;\n}\n::v-deep .el-tree-node__content{\n  height: 48px;\n  font-size: 14px\n}\n::v-deep .el-tree-node__content:hover{\n  background-color: #f5f5f5;\n}\n::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content{\n  line-height: 48px;\n  color: #333333;\n  font-weight: bold;\n  background-color: #f5f5f5;\n}\n</style>\n"]}]}