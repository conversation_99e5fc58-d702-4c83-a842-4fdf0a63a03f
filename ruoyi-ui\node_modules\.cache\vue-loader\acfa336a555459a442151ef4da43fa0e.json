{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\index.vue", "mtime": 1756287010975}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/aqsoc/hw-work", "sourcesContent": ["<template>\r\n  <div class=\"custom-container\">\r\n    <div v-show=\"!formVisible1\" class=\"custom-content-container-right\">\r\n      <div class=\"custom-content-search-box\">\r\n        <el-row :gutter=\"16\">\r\n          <el-form @submit.native.prevent>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"年度\">\r\n                <el-input v-model=\"query.year\" placeholder=\"请输入年度\" clearable>\r\n                </el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"技术支撑单位\">\r\n                <el-input v-model=\"query.supportOrgs\" placeholder=\"请输入技术支撑单位\" clearable>\r\n                </el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item class=\"custom-search-btn\">\r\n                <el-button class=\"btn1\" size=\"small\" @click=\"search()\">查询</el-button>\r\n                <el-button class=\"btn2\" size=\"small\" @click=\"reset()\">重置</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-form>\r\n        </el-row>\r\n      </div>\r\n\r\n      <div class=\"custom-content-container\">\r\n        <div class=\"common-header\">\r\n          <div><span class=\"common-head-title\">HW事务列表</span></div>\r\n          <div class=\"common-head-right\">\r\n            <el-row :gutter=\"10\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"small\"\r\n                  @click=\"addOrUpdateHandle()\"\r\n                >新增\r\n                </el-button >\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </div>\r\n        <el-table height=\"100%\" v-loading=\"listLoading\" :data=\"list\" @sort-change='sortChange'\r\n                  :span-method=\"arraySpanMethod\">\r\n          <el-table-column\r\n            prop=\"year\"\r\n            label=\"年度\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"supportOrgs\"\r\n            label=\"技术支撑单位\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"userNames\"\r\n            label=\"联络人\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"hwStart\"\r\n            label=\"HW开始时间\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"hwEnd\"\r\n            label=\"HW结束时间\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column\r\n            prop=\"createTime\"\r\n            label=\"创建时间\" align=\"left\">\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button type=\"text\" @click=\"updateHandle(scope.row, true)\">HW工作台</el-button>\r\n              <el-button type=\"text\" class=\"table-delBtn\" @click=\"handleDel(scope.row.id)\">删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination :total=\"total\" :page.sync=\"listQuery.currentPage\" :limit.sync=\"listQuery.pageSize\"\r\n                    @pagination=\"initData\"></pagination>\r\n      </div>\r\n      <Form :visible.sync=\"formVisible\" ref=\"addForm\" @refresh=\"refresh\"></Form>\r\n    </div>\r\n    <big-form :visible.sync=\"formVisible1\" ref=\"BigForm\" @refresh=\"refresh\" ></big-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Form from './form'\r\nimport BigForm from './big-form'\r\nimport {mapGetters} from \"vuex\";\r\nimport {getList, delData} from '@/api/aqsoc/work-hw/crud'\r\n\r\nexport default {\r\n  components: {Form, BigForm},\r\n  props: {},\r\n  data() {\r\n    return {\r\n      keyword: '',\r\n      expandObj: {},\r\n      query: {},\r\n      list: [],\r\n      listLoading: true,\r\n      formVisible: false,\r\n      formVisible1: false,\r\n      total: 0,\r\n      mergeList: [],\r\n      listQuery: {\r\n        currentPage: 1,\r\n        pageSize: 20\r\n      },\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters(['userInfo']),\r\n    menuId() {\r\n      return this.$route.meta.modelId || ''\r\n    }\r\n  },\r\n  created() {\r\n    this.initSearchDataAndListData()\r\n  },\r\n  methods: {\r\n    addOrUpdateHandle(id, isDetail, isAudit) {\r\n      this.formVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.addForm.init(id, isDetail, isAudit)\r\n      })\r\n    },\r\n    updateHandle(row, isDetail, isAudit) {\r\n      this.formVisible1 = true\r\n      this.$nextTick(() => {\r\n        this.$refs.BigForm.init(row, isDetail, isAudit)\r\n      })\r\n    },\r\n    arraySpanMethod({column}) {\r\n      for (let i = 0; i < this.mergeList.length; i++) {\r\n        if (column.property == this.mergeList[i].prop) {\r\n          return [this.mergeList[i].rowspan, this.mergeList[i].colspan]\r\n        }\r\n      }\r\n    },\r\n    sortChange({column, prop, order}) {\r\n      this.initData()\r\n    },\r\n    async initSearchDataAndListData() {\r\n      await this.initSearchData()\r\n      this.initData()\r\n    },\r\n    //初始化查询的默认数据\r\n    async initSearchData() {\r\n    },\r\n    initData() {\r\n      this.listLoading = true;\r\n      let _query = {\r\n        ...this.listQuery,\r\n        ...this.query,\r\n        keyword: this.keyword,\r\n        menuId: this.menuId\r\n      };\r\n      getList(_query).then(res => {\r\n        var _list = [];\r\n        for (let i = 0; i < res.data.list.length; i++) {\r\n          let _data = res.data.list[i];\r\n          _list.push(_data)\r\n        }\r\n        this.list = _list.map(o => ({\r\n          ...o,\r\n          ...this.expandObj,\r\n        }))\r\n        this.total = res.data.pagination.total\r\n        this.listLoading = false\r\n      })\r\n    },\r\n    search() {\r\n      this.listQuery.currentPage = 1\r\n      this.listQuery.pageSize = 20\r\n      this.listQuery.sort = \"desc\"\r\n      this.listQuery.sidx = \"\"\r\n      this.initData()\r\n    },\r\n    refresh(isrRefresh) {\r\n      this.formVisible = false\r\n      this.formVisible1 = false\r\n      if (isrRefresh) this.reset()\r\n    },\r\n    reset() {\r\n      for (let key in this.query) {\r\n        this.query[key] = undefined\r\n      }\r\n      this.search()\r\n    },\r\n    handleDel(id) {\r\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delData(id).then(res => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: res.msg,\r\n          });\r\n          this.initData()\r\n        })\r\n      }).catch(() => {\r\n      });\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"]}]}