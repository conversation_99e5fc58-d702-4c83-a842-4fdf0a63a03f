{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\attackViewList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\attackViewList.vue", "mtime": 1756287010958}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_AttackAlarm", "require", "_ruoyi", "_attackStage", "_interopRequireDefault", "_index", "_deptSelect", "_attackStageText", "_<PERSON><PERSON><PERSON>n", "_batchBlock", "_data", "name", "components", "AttackStageText", "DeptSelect", "AttackStage", "DetailInfo", "BatchBlock", "dicts", "props", "propsActiveName", "type", "String", "props<PERSON>ueryP<PERSON><PERSON>", "Object", "default", "currentBtn", "Number", "data", "victimIpNumData", "threatenNameDetailsQuery", "count", "showAll", "queryParams", "pageNum", "pageSize", "victimIpNumQueryParams", "hitRulesQueryParams", "rangeTime", "loading", "descLoading", "attackAlarmList", "total", "victimIpNumTotal", "hitRulesTotal", "detailDialog", "<PERSON><PERSON><PERSON><PERSON>", "hostIp", "detailType", "isAsset", "currentAssetData", "dipDrawerVisible", "dipDetailsData", "dipDrawerLoading", "<PERSON><PERSON><PERSON>", "threatenNameDrawerVisible", "threatenNameDetailsData", "threatenNameDrawerLoading", "blockingDialogVisible", "multipleSelection", "locationOptions", "value", "label", "blockStatusOptions", "riskLevelOptions", "tagColor", "tagBackgroundColor", "watch", "init", "val", "handlePropsQuery", "computed", "noMore", "disabled", "flexColumn<PERSON>idth", "columnWidth", "prop", "mounted", "query", "$route", "keys", "length", "getRiskLevelOptions", "methods", "load", "_this", "setTimeout", "startTime", "endTime", "alarmLevel", "riskLevel", "handleQuery", "_this2", "_objectSpread2", "startUpdateTime", "parseTime", "endUpdateTime", "Date", "setHours", "getList", "$nextTick", "$refs", "stage", "initAttackStage", "reset<PERSON><PERSON>y", "srcIp", "currentSelectedCard", "_this3", "params", "attackIp", "$emit", "listAttackAlarm", "then", "res", "code", "rows", "for<PERSON>ach", "e", "childrenData", "finally", "expandChange", "row", "expandRowKeys", "_this4", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getEventSegTypeList", "$set", "getTime", "stop", "handleExport", "download", "concat", "handleDetail", "assetId", "handleAtcClick", "attackSeg", "handleApplicationTagShow", "applicationList", "result", "assetName", "openDipDetails", "_this5", "getDipDetails", "updateTime", "id", "dipRenderHeader", "h", "_ref", "column", "$index", "style", "marginLeft", "color", "openThreatenNameDetails", "_this6", "getThreatenNameDetails", "handleBlocking", "_this7", "console", "log", "arr", "push", "$message", "warning", "map", "item", "Array", "from", "Set", "batchBlock", "block_ip", "join", "handleSelectionChange", "_this8", "getMulTypeDict", "dictType", "col", "list", "array<PERSON>ength", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "info", "isArray", "Math", "max", "err", "f"], "sources": ["src/views/frailty/event/component/attackViewList.vue"], "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label-width=\"100px\" label=\"最近告警时间\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"攻击者IP\" prop=\"destIp\">\n                <el-input\n                  v-model=\"queryParams.attackIp\"\n                  placeholder=\"请输入完整的攻击者IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"地理位置\" prop=\"location\">\n                <el-select v-model=\"queryParams.location\" clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in locationOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\"\n                >查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\"\n                >重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"阻断状态\" prop=\"blockStatus\">\n                <el-select v-model=\"queryParams.blockStatus\" clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in blockStatusOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"6\">\n              <el-form-item label=\"告警等级\" prop=\"riskLevel\">\n                <el-select v-model=\"queryParams.riskLevel\" clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in riskLevelOptions\"\n                    :key=\"item.dictValue\"\n                    :label=\"item.dictLabel\"\n                    :value=\"item.dictValue\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\" :style=\"showAll ? { height: 'calc(100% - 248px)' } :{ height: 'calc(100% - 208px)' }\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">攻击者视角列表</span></div>\n          <div style=\"width: 50%; margin-left: 8%\">\n<!--            <attack-stage-text ref=\"stage\"/>-->\n          </div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleBlocking\"\n                >批量阻断</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <div class=\"tableContainer\">\n        <el-table\n          height=\"100%\"\n          v-loading=\"loading\"\n          :data=\"attackAlarmList\"\n          @selection-change=\"handleSelectionChange\"\n          @expand-change=\"expandChange\" >\n          <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n          <el-table-column label=\"攻击者IP\" min-width=\"150\" prop=\"attackIp\" :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span>{{ scope.row.attackIp }}</span>\n                <img v-if=\"scope.row.isBlocking\" style=\"width: 24px;margin-left: 10px\" src=\"@/assets/images/block.png\" alt=\"\">\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"关联业务系统\" prop=\"businessApplicationList\" width=\"130\">\n            <template slot-scope=\"scope\">\n              <el-tooltip placement=\"bottom-end\" effect=\"light\" v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\">\n                <div slot=\"content\">\n                  <div v-for=\"(item,tagIndex) in scope.row.businessApplications\" :key=\"item.assetId\" class=\"overflow-tag\" v-if=\"tagIndex <= 5\">\n                    <el-tag type=\"primary\"><span>{{item.assetName}}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 5\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\"><span>{{handleApplicationTagShow(scope.row.businessApplications)}}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"告警等级\" prop=\"riskLevel\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <dict-tag :options=\"dict.type.alarm_attack_risk_level\" :value=\"scope.row.riskLevel\"/>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"攻击者标签\"\n            prop=\"tags\"\n            :width=\"flexColumnWidth\"\n            :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span class=\"tag-name\" v-for=\"(tag,index) in scope.row.tags\" :style=\"{ backgroundColor: tagBackgroundColor[index], color: tagColor[index], marginRight: scope.row.tags.length > 0 ? '5px' : '0'}\">{{ tag.tagName }}</span>\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"地理位置\" min-width=\"120\" prop=\"location\"  />\n          <el-table-column label=\"攻击目标IP数\" min-width=\"120\" prop=\"victimIpNums\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"text\" @click=\"openDipDetails(scope.row)\">{{scope.row.victimIpNums}}</el-button>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"命中规则数\" min-width=\"120\" prop=\"attackTypeNums\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"text\" @click=\"openThreatenNameDetails(scope.row)\">{{scope.row.attackTypeNums}}</el-button>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"告警数量\" min-width=\"120\" prop=\"attackNums\"  />\n          <el-table-column label=\"最早告警时间\" min-width=\"120\" prop=\"startTime\"  />\n          <el-table-column label=\"最近告警时间\" min-width=\"120\" prop=\"updateTime\"  />\n          <el-table-column label=\"同步状态\" prop=\"synchronizationStatus\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.synchronizationStatus === '0'\">未同步</span>\n              <span v-else-if=\"scope.row.synchronizationStatus === '1'\">已同步</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"120\" fixed=\"right\" :show-overflow-tooltip=\"false\" class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button\n                :disabled=\"!scope.row.attackIp\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleDetail(scope.row)\"\n              >详情\n              </el-button>\n              <el-button\n                :disabled=\"!scope.row.attackIp\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleBlocking(scope.row)\"\n              >阻断\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n\n        </div>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <el-dialog title=\"详情\" :visible.sync=\"detailDialog\" width=\"70%\" append-to-body>\n      <detail-info v-if=\"detailDialog\" :host-ip=\"hostIp\" :detail-type=\"detailType\" :is-asset=\"isAsset\" :current-asset-data=\"currentAssetData\" />\n    </el-dialog>\n\n<!--    <el-drawer\n      :visible.sync=\"dipDrawerVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      <el-table :data=\"dipDetailsData\" v-loading=\"dipDrawerLoading\">\n        <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>\n        <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n        <el-table-column property=\"dip\" label=\"目标IP\" :render-header=\"dipRenderHeader\">\n          <template slot-scope=\"scope\">\n            <span>{{scope.row.dip}}</span>\n            <span style=\"color: #ADADAD;margin-left: 5px;\" v-if=\"scope.row.serverName\">[{{scope.row.serverName}}]</span>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-drawer>-->\n\n    <el-dialog\n      :visible.sync=\"dipDrawerVisible\"\n      @close=\"victimIpNumTotal = 0\"\n      width=\"60%\" height=\"50%\" class=\"dip-dialog\">\n      <el-table :data=\"dipDetailsData\" v-loading=\"dipDrawerLoading\">\n<!--         <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>-->\n         <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n         <el-table-column property=\"dip\" label=\"目标IP\" :render-header=\"dipRenderHeader\">\n           <template slot-scope=\"scope\">\n             <span>{{scope.row.dip}}</span>\n             <span style=\"color: #ADADAD;margin-left: 5px;\" v-if=\"scope.row.serverName\">[{{scope.row.serverName}}]</span>\n           </template>\n         </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"victimIpNumTotal>0\"\n        :total=\"victimIpNumTotal\"\n        :page.sync=\"victimIpNumQueryParams.pageNum\"\n        :limit.sync=\"victimIpNumQueryParams.pageSize\"\n        @pagination=\"openDipDetails(victimIpNumData)\"\n      />\n    </el-dialog>\n\n    <el-dialog\n      :visible.sync=\"threatenNameDrawerVisible\"\n      @close=\"hitRulesTotal = 0\"\n      width=\"60%\" height=\"50%\" class=\"dip-dialog\">\n      <el-table :data=\"threatenNameDetailsData\" v-loading=\"threatenNameDrawerLoading\">\n<!--        <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>-->\n        <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n        <el-table-column property=\"threatenName\" label=\"[规则ID] 告警名称\">\n          <template slot-scope=\"scope\">\n            <span :class=\"scope.row.alarmLevel?scope.row.alarmLevel===4?'threatenName-error':scope.row.alarmLevel===3?'threatenName-warn':scope.row.alarmLevel===2?'threatenName-success':'':''\">{{scope.row.threatenName}}</span>\n          </template>\n        </el-table-column>\n        <el-table-column property=\"threatenType\" label=\"攻击类型\"></el-table-column>\n        <el-table-column property=\"count\" label=\"告警数量\"></el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"hitRulesTotal>0\"\n        :total=\"hitRulesTotal\"\n        :page.sync=\"hitRulesQueryParams.pageNum\"\n        :limit.sync=\"hitRulesQueryParams.pageSize\"\n        @pagination=\"openThreatenNameDetails(threatenNameDetailsQuery)\"\n      />\n    </el-dialog>\n\n<!--    <el-drawer\n      :visible.sync=\"threatenNameDrawerVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      <el-table :data=\"threatenNameDetailsData\" v-loading=\"threatenNameDrawerLoading\">\n        <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>\n        <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n        <el-table-column property=\"threatenName\" label=\"[规则ID] 告警名称\">\n          <template slot-scope=\"scope\">\n            <span :class=\"scope.row.alarmLevel?scope.row.alarmLevel===4?'threatenName-error':scope.row.alarmLevel===3?'threatenName-warn':scope.row.alarmLevel===2?'threatenName-success':'':''\">{{scope.row.threatenName}}</span>\n          </template>\n        </el-table-column>\n        <el-table-column property=\"threatenType\" label=\"攻击类型\"></el-table-column>\n        <el-table-column property=\"count\" label=\"告警数量\"></el-table-column>\n      </el-table>\n    </el-drawer>-->\n\n    <batch-block :visible.sync=\"blockingDialogVisible\" ref=\"batchBlock\" />\n  </div>\n</template>\n\n<script>\nimport { listAttackAlarm,getDipDetails,getThreatenNameDetails } from \"@/api/threaten/AttackAlarm\";\nimport { parseTime } from \"../../../../utils/ruoyi\";\nimport AttackStage from \"../../../threat/overview/attackStage\";\nimport DetailInfo from \"./attack_detail/index.vue\"\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport AttackStageText from '@/views/threat/overview/attackStageText.vue'\nimport {addBlockIp} from \"@/api/threaten/threatenWarn\";\nimport BatchBlock from \"@/views/frailty/event/component/batchBlock.vue\";\nimport {getMulTypeDict} from \"@/api/system/dict/data\";\n\nexport default {\n  name: \"attackViewList\",\n  components: { AttackStageText, DeptSelect, AttackStage, DetailInfo,BatchBlock },\n  dicts: ['alarm_attack_risk_level'],\n  props: {\n    propsActiveName: {\n      type: String\n    },\n    propsQueryParams: {\n      type: Object,\n      default: function () {\n        return null\n      }\n    },\n    currentBtn: {\n      type: Number,\n      default: null\n    }\n  },\n  data() {\n    return {\n      victimIpNumData: {},\n      threatenNameDetailsQuery:{},\n      count: 10,\n      showAll: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      victimIpNumQueryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      hitRulesQueryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      rangeTime: null,\n      loading: false,\n      descLoading: false,\n      attackAlarmList: [],\n      total: 0,\n      victimIpNumTotal: 0,\n      hitRulesTotal: 0,\n      detailDialog: false,\n      descKey: 0,\n      hostIp: '',\n      detailType: 'attack',\n      isAsset: false,\n      currentAssetData: {},\n      dipDrawerVisible: false,\n      dipDetailsData: [],\n      dipDrawerLoading: false,\n      threatenName: 1,\n      threatenNameDrawerVisible: false,\n      threatenNameDetailsData: [],\n      threatenNameDrawerLoading: false,\n      blockingDialogVisible: false,\n      multipleSelection: [],\n      locationOptions: [\n        {value: '1',label: '内网'},\n        {value: '2',label: '外网'},\n        {value: '3',label: '内/外网'},\n      ],\n      blockStatusOptions: [\n        {value: 1,label: '正在阻断'},\n        {value: 2,label: '曾经阻断'},\n      ],\n      riskLevelOptions: [],\n      tagColor: ['#c86c00','#bf1a1a','#1a2bbf','#901abf','#1a2bbf'],\n      tagBackgroundColor: ['#ff9f1933','#fd828233','#7899e033','#c278e033','#7899e033'],\n    }\n  },\n  watch: {\n    propsActiveName() {\n      this.init()\n    },\n    propsQueryParams(val){\n      this.handlePropsQuery(val);\n    },\n  },\n  computed: {\n    noMore () {\n      return this.count >= 20\n    },\n    disabled () {\n      return this.loading || this.noMore\n    },\n    flexColumnWidth() {\n      return this.columnWidth({prop: 'tags', label: '攻击者标签'}, this.attackAlarmList);\n    }\n  },\n  mounted() {\n    let query = this.$route.query;\n    if(!query || Object.keys(query).length < 1){\n      this.init();\n    }else {\n      this.handlePropsQuery(query);\n    }\n    this.getRiskLevelOptions();\n  },\n  methods: {\n    load () {\n      this.loading = true\n      setTimeout(() => {\n        this.count += 2\n        this.loading = false\n      }, 2000)\n    },\n    handlePropsQuery(val) {\n      if(val && Object.keys(val).length > 0){\n        this.queryParams = val;\n        if(val.startTime && val.endTime){\n          this.rangeTime = [val.startTime, val.endTime];\n        }\n        if(val.alarmLevel){\n          if(val.alarmLevel === '2'){\n            this.queryParams.riskLevel = '1';\n          }else if(val.alarmLevel === '3'){\n            this.queryParams.riskLevel = '2';\n          }else if(val.alarmLevel === '4'){\n            this.queryParams.riskLevel = '3';\n          }else {\n            this.queryParams.riskLevel = '-99';\n          }\n        }else {\n          this.queryParams.riskLevel = null;\n        }\n        this.handleQuery();\n      }\n    },\n    init() {\n      this.handleQuery()\n      //this.getList()\n    },\n    handleQuery() {\n      this.queryParams = {...this.queryParams,...this.propsQueryParams};\n      if(this.queryParams.alarmLevel){\n        if(this.queryParams.alarmLevel === '2'){\n          this.queryParams.riskLevel = '1';\n        }else if(this.queryParams.alarmLevel === '3'){\n          this.queryParams.riskLevel = '2';\n        }else if(this.queryParams.alarmLevel === '4'){\n          this.queryParams.riskLevel = '3';\n        }else {\n          this.queryParams.riskLevel = '-99';\n        }\n      }else {\n        this.queryParams.riskLevel = null;\n      }\n      this.queryParams.pageNum = 1\n      this.queryParams.pageSize = 10\n      if (this.rangeTime !== null) {\n        this.queryParams.startUpdateTime = parseTime(this.rangeTime[0]);\n        this.queryParams.endUpdateTime = parseTime(this.rangeTime[1]);\n      } else {\n        this.queryParams.startUpdateTime = null;\n        this.queryParams.endUpdateTime = null;\n      }\n\n      if(!this.queryParams.startUpdateTime){\n        this.queryParams.startUpdateTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'); // 一周前，时间部分为 00:00:00\n      }\n      if(!this.queryParams.endUpdateTime){\n        this.queryParams.endUpdateTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'); // 当前日期，时间部分为 23:59:59\n      }\n      this.rangeTime = [this.queryParams.startUpdateTime, this.queryParams.endUpdateTime];\n      this.getList()\n      this.$nextTick(() => {\n        this.$refs.stage && this.$refs.stage.initAttackStage({\n          ...this.queryParams,\n          startTime: this.queryParams.startUpdateTime,\n          endTime: this.queryParams.endUpdateTime\n        })\n      })\n    },\n    resetQuery() {\n      this.queryParams = {\n        srcIp: '',\n        alarmLevel: ''\n      }\n      this.propsQueryParams.riskLevel = this.queryParams.riskLevel;\n      this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel;\n      if(this.$refs.stage){\n        this.$refs.stage.currentSelectedCard = null;\n      }\n      this.rangeTime = []\n      this.handleQuery();\n    },\n    getList() {\n      this.loading = true;\n      //同步请求类型统计数据\n      let params = {...this.queryParams};\n      params.srcIp = this.queryParams.attackIp;\n      params.startTime = this.queryParams.startUpdateTime;\n      params.endTime = this.queryParams.endUpdateTime;\n      if(params.riskLevel){\n        if(params.riskLevel === '1'){\n          params.alarmLevel = '2';\n        }else if(params.riskLevel === '2'){\n          params.alarmLevel = '3';\n        }else if(params.riskLevel === '3'){\n          params.alarmLevel = '4';\n        }\n      }\n      this.$emit('getList',params);\n      listAttackAlarm(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.attackAlarmList = res.rows\n          this.attackAlarmList.forEach(e => {\n            e.childrenData = {}\n          })\n          this.total = res.total\n        }\n      }).finally(()=>{\n        this.loading = false;\n      })\n    },\n    async expandChange(row, expandRowKeys) {\n      if (expandRowKeys.length > 0) {\n        getEventSegTypeList({\n          attackIp: row.attackIp,\n          startTime: this.queryParams.startUpdateTime,\n          endTime: this.queryParams.endUpdateTime\n        }).then(res => {\n          if (res.code === 200) {\n            const childrenData = res.data\n            this.$set(row, 'childrenData', childrenData)\n            this.descKey = new Date().getTime()\n          }\n        })\n      }\n    },\n    handleExport() {\n      this.download('/threaten/AttackAlarm/export', {\n        ...this.queryParams\n      }, `攻击者视角数据_${new Date().getTime()}.xlsx`)\n    },\n    handleDetail(row) {\n      if (row.attackIp) {\n        this.detailDialog = true\n        this.hostIp = row.attackIp;\n        this.isAsset = row.assetId;\n        this.currentAssetData = row;\n      }\n    },\n    handleAtcClick(attackSeg){\n      this.queryParams.attackSeg = attackSeg;\n      this.handleQuery();\n    },\n    handleApplicationTagShow(applicationList){\n      if(!applicationList || applicationList.length < 1){\n        return '';\n      }\n      let result = applicationList[0].assetName;\n      if(applicationList.length > 1){\n        result += '...';\n      }\n      return result;\n    },\n    openDipDetails(row){\n      // this.victimIpNumTotal = 0 ;\n        this.dipDetailsData = [];\n      this.dipDrawerVisible = true;\n      this.dipDrawerLoading = true;\n      this.victimIpNumData = row;\n      getDipDetails({\n        attackIp: row.attackIp,\n        startTime: row.startTime,\n        updateTime: row.updateTime,\n        id: row.id,\n        pageSize: this.victimIpNumQueryParams.pageSize,\n        pageNum: this.victimIpNumQueryParams.pageNum\n      }).then(res => {\n        this.dipDetailsData = res.data.rows;\n        this.victimIpNumTotal = res.data.total;\n      }).finally(() => {\n        this.dipDrawerLoading = false;\n      })\n    },\n    dipRenderHeader(h, { column, $index }){\n      return h('div', [\n        h('span', column.label),\n        h('span', {\n          style: {\n            marginLeft: '5px',\n            color: '#ADADAD'\n          }\n        }, '[主机名称]')\n      ])\n    },\n    openThreatenNameDetails(row){\n      this.threatenNameDetailsData = [];\n      this.threatenNameDrawerVisible = true;\n      this.threatenNameDrawerLoading = true;\n      this.threatenNameDetailsQuery = row\n      getThreatenNameDetails({\n        attackIp: row.attackIp,\n        startTime: row.startTime,\n        updateTime: row.updateTime,\n        id: row.id,\n        pageSize: this.hitRulesQueryParams.pageSize,\n        pageNum: this.hitRulesQueryParams.pageNum\n      }).then(res => {\n        this.threatenNameDetailsData = res.data.rows;\n        this.hitRulesTotal = res.data.total;\n      }).finally(() => {\n        this.threatenNameDrawerLoading = false;\n      })\n    },\n    handleBlocking(row) {\n      console.log( row)\n      let arr = [];\n      if(row && row.attackIp){\n        arr.push(row.attackIp);\n      }else {\n        if (this.multipleSelection.length < 1) return this.$message.warning('请选择要阻断的ip');\n        arr = this.multipleSelection.map(item => item.attackIp);\n        arr = Array.from(new Set(arr));\n      }\n      this.blockingDialogVisible = true;\n      this.$nextTick(() => {\n        this.$refs.batchBlock && this.$refs.batchBlock.init({\n          block_ip: arr.join(';')\n        })\n      })\n    },\n    // 多选\n    handleSelectionChange(val) {\n      this.multipleSelection = val;\n    },\n    getRiskLevelOptions(){\n      getMulTypeDict({\n        dictType: 'alarm_attack_risk_level'\n      }).then(res => {\n        this.riskLevelOptions = res.data;\n      })\n    },\n    columnWidth(col, list) {\n      // 获取数组长度作为基础\n      let arrayLength = 0;\n\n      // 遍历数据列表，找出 tags 数组的最大长度\n      for (let info of list) {\n        if (info[col.prop] && Array.isArray(info[col.prop])) {\n          arrayLength = Math.max(arrayLength, info[col.prop].length);\n        }\n      }\n\n      // 设置最小宽度为140px\n      return Math.max(arrayLength * 140, 140);\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.asset-tag{\n  max-width: 100%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n.dip-dialog{\n  ::v-deep .el-dialog{\n\n  }\n  ::v-deep .el-dialog__body{\n    padding: 20px;\n    margin-top: 20px;\n  }\n}\n.attack-tag:not(:nth-child(-n+2)) {\n  margin-top: 5px;\n}\n.overflow-tag:not(:first-child){\n  margin-top: 5px;\n}\n.threatenName-error{\n  color: #F56C6C;\n}\n.threatenName-success{\n  color: #67C23A;\n}\n.threatenName-warn{\n  color: #E6A23C;\n}\n\n.tag-name {\n  display: inline-block;\n  padding: 2px 12px;\n  margin-bottom: 5px;\n  font-size: 14px;\n  font-weight: normal;\n  line-height: 1.42857143;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  background-image: none;\n  border: 1px solid transparent;\n  border-radius: 4px;\n}\n\n::v-deep .el-dialog__body {\n  max-height: 80vh;\n  padding: 0 30px 30px;\n  overflow-y: auto;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAkTA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,MAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,WAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,gBAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,aAAA,GAAAP,OAAA;AACA,IAAAQ,WAAA,GAAAL,sBAAA,CAAAH,OAAA;AACA,IAAAS,KAAA,GAAAT,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAU,IAAA;EACAC,UAAA;IAAAC,eAAA,EAAAA,wBAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,UAAA,EAAAA,cAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,KAAA;IACAC,eAAA;MACAC,IAAA,EAAAC;IACA;IACAC,gBAAA;MACAF,IAAA,EAAAG,MAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAC,UAAA;MACAL,IAAA,EAAAM,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,eAAA;MACAC,wBAAA;MACAC,KAAA;MACAC,OAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,sBAAA;QACAF,OAAA;QACAC,QAAA;MACA;MACAE,mBAAA;QACAH,OAAA;QACAC,QAAA;MACA;MACAG,SAAA;MACAC,OAAA;MACAC,WAAA;MACAC,eAAA;MACAC,KAAA;MACAC,gBAAA;MACAC,aAAA;MACAC,YAAA;MACAC,OAAA;MACAC,MAAA;MACAC,UAAA;MACAC,OAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,YAAA;MACAC,yBAAA;MACAC,uBAAA;MACAC,yBAAA;MACAC,qBAAA;MACAC,iBAAA;MACAC,eAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,kBAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAE,gBAAA;MACAC,QAAA;MACAC,kBAAA;IACA;EACA;EACAC,KAAA;IACA/C,eAAA,WAAAA,gBAAA;MACA,KAAAgD,IAAA;IACA;IACA7C,gBAAA,WAAAA,iBAAA8C,GAAA;MACA,KAAAC,gBAAA,CAAAD,GAAA;IACA;EACA;EACAE,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,YAAAzC,KAAA;IACA;IACA0C,QAAA,WAAAA,SAAA;MACA,YAAAlC,OAAA,SAAAiC,MAAA;IACA;IACAE,eAAA,WAAAA,gBAAA;MACA,YAAAC,WAAA;QAAAC,IAAA;QAAAd,KAAA;MAAA,QAAArB,eAAA;IACA;EACA;EACAoC,OAAA,WAAAA,QAAA;IACA,IAAAC,KAAA,QAAAC,MAAA,CAAAD,KAAA;IACA,KAAAA,KAAA,IAAAtD,MAAA,CAAAwD,IAAA,CAAAF,KAAA,EAAAG,MAAA;MACA,KAAAb,IAAA;IACA;MACA,KAAAE,gBAAA,CAAAQ,KAAA;IACA;IACA,KAAAI,mBAAA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MAAA,IAAAC,KAAA;MACA,KAAA9C,OAAA;MACA+C,UAAA;QACAD,KAAA,CAAAtD,KAAA;QACAsD,KAAA,CAAA9C,OAAA;MACA;IACA;IACA+B,gBAAA,WAAAA,iBAAAD,GAAA;MACA,IAAAA,GAAA,IAAA7C,MAAA,CAAAwD,IAAA,CAAAX,GAAA,EAAAY,MAAA;QACA,KAAAhD,WAAA,GAAAoC,GAAA;QACA,IAAAA,GAAA,CAAAkB,SAAA,IAAAlB,GAAA,CAAAmB,OAAA;UACA,KAAAlD,SAAA,IAAA+B,GAAA,CAAAkB,SAAA,EAAAlB,GAAA,CAAAmB,OAAA;QACA;QACA,IAAAnB,GAAA,CAAAoB,UAAA;UACA,IAAApB,GAAA,CAAAoB,UAAA;YACA,KAAAxD,WAAA,CAAAyD,SAAA;UACA,WAAArB,GAAA,CAAAoB,UAAA;YACA,KAAAxD,WAAA,CAAAyD,SAAA;UACA,WAAArB,GAAA,CAAAoB,UAAA;YACA,KAAAxD,WAAA,CAAAyD,SAAA;UACA;YACA,KAAAzD,WAAA,CAAAyD,SAAA;UACA;QACA;UACA,KAAAzD,WAAA,CAAAyD,SAAA;QACA;QACA,KAAAC,WAAA;MACA;IACA;IACAvB,IAAA,WAAAA,KAAA;MACA,KAAAuB,WAAA;MACA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAA3D,WAAA,OAAA4D,cAAA,CAAApE,OAAA,MAAAoE,cAAA,CAAApE,OAAA,WAAAQ,WAAA,QAAAV,gBAAA;MACA,SAAAU,WAAA,CAAAwD,UAAA;QACA,SAAAxD,WAAA,CAAAwD,UAAA;UACA,KAAAxD,WAAA,CAAAyD,SAAA;QACA,gBAAAzD,WAAA,CAAAwD,UAAA;UACA,KAAAxD,WAAA,CAAAyD,SAAA;QACA,gBAAAzD,WAAA,CAAAwD,UAAA;UACA,KAAAxD,WAAA,CAAAyD,SAAA;QACA;UACA,KAAAzD,WAAA,CAAAyD,SAAA;QACA;MACA;QACA,KAAAzD,WAAA,CAAAyD,SAAA;MACA;MACA,KAAAzD,WAAA,CAAAC,OAAA;MACA,KAAAD,WAAA,CAAAE,QAAA;MACA,SAAAG,SAAA;QACA,KAAAL,WAAA,CAAA6D,eAAA,OAAAC,gBAAA,OAAAzD,SAAA;QACA,KAAAL,WAAA,CAAA+D,aAAA,OAAAD,gBAAA,OAAAzD,SAAA;MACA;QACA,KAAAL,WAAA,CAAA6D,eAAA;QACA,KAAA7D,WAAA,CAAA+D,aAAA;MACA;MAEA,UAAA/D,WAAA,CAAA6D,eAAA;QACA,KAAA7D,WAAA,CAAA6D,eAAA,OAAAC,gBAAA,MAAAE,IAAA,GAAAC,QAAA;MACA;MACA,UAAAjE,WAAA,CAAA+D,aAAA;QACA,KAAA/D,WAAA,CAAA+D,aAAA,OAAAD,gBAAA,MAAAE,IAAA,GAAAC,QAAA;MACA;MACA,KAAA5D,SAAA,SAAAL,WAAA,CAAA6D,eAAA,OAAA7D,WAAA,CAAA+D,aAAA;MACA,KAAAG,OAAA;MACA,KAAAC,SAAA;QACAR,MAAA,CAAAS,KAAA,CAAAC,KAAA,IAAAV,MAAA,CAAAS,KAAA,CAAAC,KAAA,CAAAC,eAAA,KAAAV,cAAA,CAAApE,OAAA,MAAAoE,cAAA,CAAApE,OAAA,MACAmE,MAAA,CAAA3D,WAAA;UACAsD,SAAA,EAAAK,MAAA,CAAA3D,WAAA,CAAA6D,eAAA;UACAN,OAAA,EAAAI,MAAA,CAAA3D,WAAA,CAAA+D;QAAA,EACA;MACA;IACA;IACAQ,UAAA,WAAAA,WAAA;MACA,KAAAvE,WAAA;QACAwE,KAAA;QACAhB,UAAA;MACA;MACA,KAAAlE,gBAAA,CAAAmE,SAAA,QAAAzD,WAAA,CAAAyD,SAAA;MACA,KAAAnE,gBAAA,CAAAkE,UAAA,QAAAxD,WAAA,CAAAwD,UAAA;MACA,SAAAY,KAAA,CAAAC,KAAA;QACA,KAAAD,KAAA,CAAAC,KAAA,CAAAI,mBAAA;MACA;MACA,KAAApE,SAAA;MACA,KAAAqD,WAAA;IACA;IACAQ,OAAA,WAAAA,QAAA;MAAA,IAAAQ,MAAA;MACA,KAAApE,OAAA;MACA;MACA,IAAAqE,MAAA,OAAAf,cAAA,CAAApE,OAAA,WAAAQ,WAAA;MACA2E,MAAA,CAAAH,KAAA,QAAAxE,WAAA,CAAA4E,QAAA;MACAD,MAAA,CAAArB,SAAA,QAAAtD,WAAA,CAAA6D,eAAA;MACAc,MAAA,CAAApB,OAAA,QAAAvD,WAAA,CAAA+D,aAAA;MACA,IAAAY,MAAA,CAAAlB,SAAA;QACA,IAAAkB,MAAA,CAAAlB,SAAA;UACAkB,MAAA,CAAAnB,UAAA;QACA,WAAAmB,MAAA,CAAAlB,SAAA;UACAkB,MAAA,CAAAnB,UAAA;QACA,WAAAmB,MAAA,CAAAlB,SAAA;UACAkB,MAAA,CAAAnB,UAAA;QACA;MACA;MACA,KAAAqB,KAAA,YAAAF,MAAA;MACA,IAAAG,4BAAA,OAAA9E,WAAA,EAAA+E,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAP,MAAA,CAAAlE,eAAA,GAAAwE,GAAA,CAAAE,IAAA;UACAR,MAAA,CAAAlE,eAAA,CAAA2E,OAAA,WAAAC,CAAA;YACAA,CAAA,CAAAC,YAAA;UACA;UACAX,MAAA,CAAAjE,KAAA,GAAAuE,GAAA,CAAAvE,KAAA;QACA;MACA,GAAA6E,OAAA;QACAZ,MAAA,CAAApE,OAAA;MACA;IACA;IACAiF,YAAA,WAAAA,aAAAC,GAAA,EAAAC,aAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAnG,OAAA,mBAAAoG,oBAAA,CAAApG,OAAA,IAAAqG,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAApG,OAAA,IAAAuG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACA,IAAAV,aAAA,CAAAzC,MAAA;gBACAoD,mBAAA;kBACAxB,QAAA,EAAAY,GAAA,CAAAZ,QAAA;kBACAtB,SAAA,EAAAoC,MAAA,CAAA1F,WAAA,CAAA6D,eAAA;kBACAN,OAAA,EAAAmC,MAAA,CAAA1F,WAAA,CAAA+D;gBACA,GAAAgB,IAAA,WAAAC,GAAA;kBACA,IAAAA,GAAA,CAAAC,IAAA;oBACA,IAAAI,YAAA,GAAAL,GAAA,CAAArF,IAAA;oBACA+F,MAAA,CAAAW,IAAA,CAAAb,GAAA,kBAAAH,YAAA;oBACAK,MAAA,CAAA7E,OAAA,OAAAmD,IAAA,GAAAsC,OAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAL,QAAA,CAAAM,IAAA;UAAA;QAAA,GAAAT,OAAA;MAAA;IACA;IACAU,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,qCAAA7C,cAAA,CAAApE,OAAA,MACA,KAAAQ,WAAA,iDAAA0G,MAAA,CACA,IAAA1C,IAAA,GAAAsC,OAAA;IACA;IACAK,YAAA,WAAAA,aAAAnB,GAAA;MACA,IAAAA,GAAA,CAAAZ,QAAA;QACA,KAAAhE,YAAA;QACA,KAAAE,MAAA,GAAA0E,GAAA,CAAAZ,QAAA;QACA,KAAA5D,OAAA,GAAAwE,GAAA,CAAAoB,OAAA;QACA,KAAA3F,gBAAA,GAAAuE,GAAA;MACA;IACA;IACAqB,cAAA,WAAAA,eAAAC,SAAA;MACA,KAAA9G,WAAA,CAAA8G,SAAA,GAAAA,SAAA;MACA,KAAApD,WAAA;IACA;IACAqD,wBAAA,WAAAA,yBAAAC,eAAA;MACA,KAAAA,eAAA,IAAAA,eAAA,CAAAhE,MAAA;QACA;MACA;MACA,IAAAiE,MAAA,GAAAD,eAAA,IAAAE,SAAA;MACA,IAAAF,eAAA,CAAAhE,MAAA;QACAiE,MAAA;MACA;MACA,OAAAA,MAAA;IACA;IACAE,cAAA,WAAAA,eAAA3B,GAAA;MAAA,IAAA4B,MAAA;MACA;MACA,KAAAjG,cAAA;MACA,KAAAD,gBAAA;MACA,KAAAE,gBAAA;MACA,KAAAxB,eAAA,GAAA4F,GAAA;MACA,IAAA6B,0BAAA;QACAzC,QAAA,EAAAY,GAAA,CAAAZ,QAAA;QACAtB,SAAA,EAAAkC,GAAA,CAAAlC,SAAA;QACAgE,UAAA,EAAA9B,GAAA,CAAA8B,UAAA;QACAC,EAAA,EAAA/B,GAAA,CAAA+B,EAAA;QACArH,QAAA,OAAAC,sBAAA,CAAAD,QAAA;QACAD,OAAA,OAAAE,sBAAA,CAAAF;MACA,GAAA8E,IAAA,WAAAC,GAAA;QACAoC,MAAA,CAAAjG,cAAA,GAAA6D,GAAA,CAAArF,IAAA,CAAAuF,IAAA;QACAkC,MAAA,CAAA1G,gBAAA,GAAAsE,GAAA,CAAArF,IAAA,CAAAc,KAAA;MACA,GAAA6E,OAAA;QACA8B,MAAA,CAAAhG,gBAAA;MACA;IACA;IACAoG,eAAA,WAAAA,gBAAAC,CAAA,EAAAC,IAAA;MAAA,IAAAC,MAAA,GAAAD,IAAA,CAAAC,MAAA;QAAAC,MAAA,GAAAF,IAAA,CAAAE,MAAA;MACA,OAAAH,CAAA,SACAA,CAAA,SAAAE,MAAA,CAAA9F,KAAA,GACA4F,CAAA;QACAI,KAAA;UACAC,UAAA;UACAC,KAAA;QACA;MACA,aACA;IACA;IACAC,uBAAA,WAAAA,wBAAAxC,GAAA;MAAA,IAAAyC,MAAA;MACA,KAAA1G,uBAAA;MACA,KAAAD,yBAAA;MACA,KAAAE,yBAAA;MACA,KAAA3B,wBAAA,GAAA2F,GAAA;MACA,IAAA0C,mCAAA;QACAtD,QAAA,EAAAY,GAAA,CAAAZ,QAAA;QACAtB,SAAA,EAAAkC,GAAA,CAAAlC,SAAA;QACAgE,UAAA,EAAA9B,GAAA,CAAA8B,UAAA;QACAC,EAAA,EAAA/B,GAAA,CAAA+B,EAAA;QACArH,QAAA,OAAAE,mBAAA,CAAAF,QAAA;QACAD,OAAA,OAAAG,mBAAA,CAAAH;MACA,GAAA8E,IAAA,WAAAC,GAAA;QACAiD,MAAA,CAAA1G,uBAAA,GAAAyD,GAAA,CAAArF,IAAA,CAAAuF,IAAA;QACA+C,MAAA,CAAAtH,aAAA,GAAAqE,GAAA,CAAArF,IAAA,CAAAc,KAAA;MACA,GAAA6E,OAAA;QACA2C,MAAA,CAAAzG,yBAAA;MACA;IACA;IACA2G,cAAA,WAAAA,eAAA3C,GAAA;MAAA,IAAA4C,MAAA;MACAC,OAAA,CAAAC,GAAA,CAAA9C,GAAA;MACA,IAAA+C,GAAA;MACA,IAAA/C,GAAA,IAAAA,GAAA,CAAAZ,QAAA;QACA2D,GAAA,CAAAC,IAAA,CAAAhD,GAAA,CAAAZ,QAAA;MACA;QACA,SAAAlD,iBAAA,CAAAsB,MAAA,kBAAAyF,QAAA,CAAAC,OAAA;QACAH,GAAA,QAAA7G,iBAAA,CAAAiH,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAhE,QAAA;QAAA;QACA2D,GAAA,GAAAM,KAAA,CAAAC,IAAA,KAAAC,GAAA,CAAAR,GAAA;MACA;MACA,KAAA9G,qBAAA;MACA,KAAA0C,SAAA;QACAiE,MAAA,CAAAhE,KAAA,CAAA4E,UAAA,IAAAZ,MAAA,CAAAhE,KAAA,CAAA4E,UAAA,CAAA7G,IAAA;UACA8G,QAAA,EAAAV,GAAA,CAAAW,IAAA;QACA;MACA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAA/G,GAAA;MACA,KAAAV,iBAAA,GAAAU,GAAA;IACA;IACAa,mBAAA,WAAAA,oBAAA;MAAA,IAAAmG,MAAA;MACA,IAAAC,oBAAA;QACAC,QAAA;MACA,GAAAvE,IAAA,WAAAC,GAAA;QACAoE,MAAA,CAAArH,gBAAA,GAAAiD,GAAA,CAAArF,IAAA;MACA;IACA;IACA+C,WAAA,WAAAA,YAAA6G,GAAA,EAAAC,IAAA;MACA;MACA,IAAAC,WAAA;;MAEA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAnK,OAAA,EACAgK,IAAA;QAAAI,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAhI,KAAA;UACA,IAAAoI,IAAA,CAAAT,GAAA,CAAA5G,IAAA,KAAAkG,KAAA,CAAAoB,OAAA,CAAAD,IAAA,CAAAT,GAAA,CAAA5G,IAAA;YACA8G,WAAA,GAAAS,IAAA,CAAAC,GAAA,CAAAV,WAAA,EAAAO,IAAA,CAAAT,GAAA,CAAA5G,IAAA,EAAAK,MAAA;UACA;QACA;;QAEA;MAAA,SAAAoH,GAAA;QAAAV,SAAA,CAAAtE,CAAA,CAAAgF,GAAA;MAAA;QAAAV,SAAA,CAAAW,CAAA;MAAA;MACA,OAAAH,IAAA,CAAAC,GAAA,CAAAV,WAAA;IACA;EACA;AACA", "ignoreList": []}]}