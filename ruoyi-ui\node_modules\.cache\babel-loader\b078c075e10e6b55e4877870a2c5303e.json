{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\threaten\\AttackAlarm.js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\threaten\\AttackAlarm.js", "mtime": 1756287010990}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1751956515056}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listAttackAlarm", "query", "request", "url", "method", "params", "getAttackAlarm", "id", "getDipDetails", "getThreatenNameDetails", "groupAlarmLevelStatistics", "addAttackAlarm", "data", "updateAttackAlarm", "delAttackAlarm"], "sources": ["E:/wsh/augment_workspace/aqsoc-main/ruoyi-ui/src/api/threaten/AttackAlarm.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询攻击者视角告警列列表\r\nexport function listAttackAlarm(query) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询攻击者视角告警列详细\r\nexport function getAttackAlarm(id) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询攻击目标IP列表\r\nexport function getDipDetails(query) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/getDipDetails',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询告警类型列表\r\nexport function getThreatenNameDetails(query) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/getThreatenNameDetails',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function groupAlarmLevelStatistics(query) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/groupAlarmLevelStatistics',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 新增攻击者视角告警列\r\nexport function addAttackAlarm(data) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改攻击者视角告警列\r\nexport function updateAttackAlarm(data) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除攻击者视角告警列\r\nexport function delAttackAlarm(id) {\r\n  return request({\r\n    url: '/threaten/AttackAlarm/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,EAAE,EAAE;EACjC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,EAAE;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACP,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,sBAAsBA,CAACR,KAAK,EAAE;EAC5C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASS,yBAAyBA,CAACT,KAAK,EAAE;EAC/C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iDAAiD;IACtDC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iBAAiBA,CAACD,IAAI,EAAE;EACtC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,cAAcA,CAACP,EAAE,EAAE;EACjC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,EAAE;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}