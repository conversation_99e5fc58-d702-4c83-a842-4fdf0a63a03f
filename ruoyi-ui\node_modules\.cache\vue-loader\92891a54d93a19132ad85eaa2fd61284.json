{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\plan.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\plan.vue", "mtime": 1756264471498}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RXb3JrSHdUYXNrLCBnZXRXb3JrSHdUYXNrLCBkZWxXb3JrSHdUYXNrLCBhZGRXb3JrSHdUYXNrLCB1cGRhdGVXb3JrSHdUYXNrLGdldFN0YWdlVHJlZSB9IGZyb20gIkAvYXBpL2Fxc29jL3dvcmstaHcvd29ya0h3VGFzayI7CmltcG9ydCB7bGlzdE9wZXJhdGVXb3JrfSBmcm9tICJAL2FwaS9vcGVyYXRlV29yay9vcGVyYXRlV29yayIKaW1wb3J0IFVzZXJTZWxlY3QgZnJvbSAnQC92aWV3cy9oaGxDb2RlL2NvbXBvbmVudC91c2VyU2VsZWN0JzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJQbGFuIiwKICBjb21wb25lbnRzOiB7VXNlclNlbGVjdH0sCiAgZGljdHM6IFsnaHdfc3RhZ2VfY2xhc3MnXSwKICBwcm9wczp7CiAgICB3b3JrSW5mbzogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICBkZWZhdWx0OiB7fQogICAgfSwKICAgIHNob3c6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDooajmoLzmlbDmja4KICAgICAgZGF0YUxpc3Q6IG51bGwsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIHRhc2tOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Lu75Yqh5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1heDogMjAsIG1lc3NhZ2U6ICLplb/luqbkuI3og73otoXov4cyMOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBjb250ZW50OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Lu75Yqh5YaF5a655LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1heDogNTAwLCBtZXNzYWdlOiAi6ZW/5bqm5LiN6IO96LaF6L+HNTAw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHN0YXJ0VGltZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS7u+WKoeW8gOWni+aXtumXtOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBlbmRUaW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Lu75Yqh5a6M5oiQ5pe26Ze05LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIG1hbmFnZVVzZXI6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLotKPku7vkurrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgb3BlcmF0ZVdvcmtJZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWFs+iBlOS6i+WKoeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBzdGFnZUNsYXNzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAiSFfpmLbmrrXkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgZm9ybToge30sCiAgICAgIHN0YWdlTGlzdDogW10sCiAgICAgIG9wZXJhdGVXb3JrTGlzdDogW10sCiAgICAgIHRpdGxlOiAnJywKICAgICAgYnRuTG9hZGluZzogZmFsc2UsCiAgICAgIHN0YXJ0UGlja2VyT3B0aW9uczogewogICAgICAgIGRpc2FibGVkRGF0ZTogKHRpbWUpID0+IHsKICAgICAgICAgIHJldHVybiBuZXcgRGF0ZSh0aW1lKS5nZXRUaW1lKCkgPCBuZXcgRGF0ZSh0aGlzLndvcmtJbmZvLmh3U3RhcnQpLmdldFRpbWUoKSB8fCBuZXcgRGF0ZSh0aW1lKS5nZXRUaW1lKCkgPiBuZXcgRGF0ZSh0aGlzLmZvcm0uZW5kVGltZT90aGlzLmZvcm0uZW5kVGltZSA6IHRoaXMud29ya0luZm8uaHdFbmQpLmdldFRpbWUoKTsKICAgICAgICB9LAogICAgICB9LAogICAgICBlbmRQaWNrZXJPcHRpb25zOiB7CiAgICAgICAgZGlzYWJsZWREYXRlOiAodGltZSkgPT4gewogICAgICAgICAgcmV0dXJuIG5ldyBEYXRlKHRpbWUpLmdldFRpbWUoKSA8IG5ldyBEYXRlKHRoaXMuZm9ybS5zdGFydFRpbWU/dGhpcy5mb3JtLnN0YXJ0VGltZSA6IHRoaXMud29ya0luZm8uaHdTdGFydCkuZ2V0VGltZSgpIHx8IG5ldyBEYXRlKHRpbWUpLmdldFRpbWUoKSA+IG5ldyBEYXRlKHRoaXMud29ya0luZm8uaHdFbmQpLmdldFRpbWUoKTsKICAgICAgICB9LAogICAgICB9LAogICAgICBkZWZhdWx0U3RhcnRUaW1lOiBuZXcgRGF0ZSh0aGlzLndvcmtJbmZvLmh3U3RhcnQpLAogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5xdWVyeVBhcmFtcy53b3JrSWQgPSB0aGlzLndvcmtJbmZvLmlkOwogICAgdGhpcy5nZXRTdGFnZUxpc3QoKTsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXRPcGVyYXRlV29ya0xpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIHNlYXJjaCgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOafpeivokhX5LqL5Yqh5Lu75Yqh5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0V29ya0h3VGFzayh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRhdGFMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGdldFN0YWdlTGlzdCgpewogICAgICBnZXRTdGFnZVRyZWUoe3dvcmtJZDogdGhpcy53b3JrSW5mby5pZH0pLnRoZW4ocmVzID0+IHsKICAgICAgICBsZXQgYXJyID0gcmVzLmRhdGE7CiAgICAgICAgbGV0IGZpcnN0ID0gewogICAgICAgICAgdmFsdWU6IG51bGwsCiAgICAgICAgICBsYWJlbDogJ+WFqOmDqOmYtuautScsCiAgICAgICAgICBzb3J0OiAwLAogICAgICAgICAgY291bnQ6IDAKICAgICAgICB9OwogICAgICAgIGlmKGFyciAmJiBhcnIubGVuZ3RoPjApewogICAgICAgICAgYXJyLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgIGZpcnN0LmNvdW50ICs9IGl0ZW0uY291bnQ7CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgICBhcnIudW5zaGlmdChmaXJzdCk7CiAgICAgICAgdGhpcy5zdGFnZUxpc3QgPSBhcnI7CiAgICAgIH0pCiAgICB9LAogICAgZ2V0T3BlcmF0ZVdvcmtMaXN0KCl7CiAgICAgIGxpc3RPcGVyYXRlV29yayh7cXVlcnlBbGxEYXRhOiB0cnVlfSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMub3BlcmF0ZVdvcmtMaXN0ID0gcmVzLnJvd3M7CiAgICAgIH0pCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75YqgSFfkuovliqHku7vliqEiOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcwogICAgICBnZXRXb3JrSHdUYXNrKGlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLlIV+S6i+WKoeS7u+WKoSI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaRIV+S6i+WKoeS7u+WKoee8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsV29ya0h3VGFzayhpZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICB9KTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgd29ya0lkOiB0aGlzLndvcmtJbmZvLmlkCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgYmFjaygpewogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6c2hvdycsZmFsc2UpOwogICAgfSwKICAgIHN1Ym1pdEZvcm0oKXsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWU7CiAgICAgICAgICB0aGlzLmZvcm0ud29ya0lkID0gdGhpcy53b3JrSW5mby5pZDsKICAgICAgICAgIGxldCBjdXJyZW50Rm9ybSA9IHsuLi50aGlzLmZvcm19OwogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZVdvcmtId1Rhc2soY3VycmVudEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KS5maW5hbGx5KCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRXb3JrSHdUYXNrKGN1cnJlbnRGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSkuZmluYWxseSgoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgdGFiQ2xpY2sodGFiKXsKICAgICAgaWYodGFiLnBhbmVOYW1lICE9PSAnMCcpewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhZ2VDbGFzcyA9IHRhYi5wYW5lTmFtZTsKICAgICAgfWVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhZ2VDbGFzcyA9IG51bGw7CiAgICAgIH0KICAgICAgdGhpcy5zZWFyY2goKTsKICAgIH0sCiAgICBnZXRQaWNrZXJPcHRpb25zKCl7CiAgICAgIGxldCBzdGFydFRpbWUgPSB0aGlzLndvcmtJbmZvLmh3U3RhcnQ7CiAgICAgIGxldCBlbmRUaW1lID0gdGhpcy53b3JrSW5mby5od0VuZDsKICAgICAgcmV0dXJuIHsKICAgICAgICBkaXNhYmxlZERhdGU6ICh0aW1lKSA9PiB7CiAgICAgICAgICBjb25zb2xlLmxvZyh0aW1lKQogICAgICAgICAgcmV0dXJuIG5ldyBEYXRlKHRpbWUpLmdldFRpbWUoKSA8IG5ldyBEYXRlKHN0YXJ0VGltZSkuZ2V0VGltZSgpIHx8IG5ldyBEYXRlKHRpbWUpLmdldFRpbWUoKSA+IG5ldyBEYXRlKGVuZFRpbWUpLmdldFRpbWUoKTsKICAgICAgICB9LAogICAgICB9CiAgICB9LAogIH0KfTsK"}, {"version": 3, "sources": ["plan.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "plan.vue", "sourceRoot": "src/views/aqsoc/hw-work", "sourcesContent": ["<template>\n  <div class=\"main\">\n    <div class=\"head-box\">\n      <div class=\"left\">\n        {{workInfo.year}}HW计划【{{workInfo.hwStart}} 至 {{workInfo.hwEnd}}】\n      </div>\n      <div class=\"right\">\n        <el-button class=\"btn2\" size=\"small\" @click=\"back\">返回</el-button>\n      </div>\n    </div>\n    <div class=\"custom-container\">\n      <div class=\"custom-tree-container\">\n        <div class=\"head-container\">\n          <el-input\n            v-model=\"queryParams.taskName\"\n            placeholder=\"请输入任务名称\"\n            clearable\n            size=\"medium\"\n            prefix-icon=\"el-icon-search\"\n            style=\"margin-bottom: 15px\"\n            @input=\"search\"\n          />\n        </div>\n        <div class=\"head-container\">\n          <el-tabs tab-position=\"left\" style=\"height: 100%;\" class=\"work-tabs\" @tab-click=\"tabClick\">\n            <el-tab-pane :label=\"tabItem.label\" v-for=\"tabItem in stageList\"><div slot=\"label\" class=\"work-tabs-label\">{{`${tabItem.label}（${tabItem.count}）`}}</div></el-tab-pane>\n          </el-tabs>\n        </div>\n      </div>\n      <div class=\"custom-content-container-right\">\n        <div class=\"custom-content-container\">\n          <div class=\"common-header\">\n            <div><span class=\"common-head-title\">任务列表</span></div>\n            <div class=\"common-head-right\">\n              <el-row :gutter=\"10\">\n                <el-col :span=\"1.5\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"small\"\n                    @click=\"handleAdd\"\n                  >新增</el-button>\n                </el-col>\n              </el-row>\n            </div>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"dataList\"\n            ref=\"table\"\n            height=\"100%\">\n            <el-table-column label=\"任务名称\" align=\"center\" prop=\"taskName\"/>\n            <el-table-column label=\"所属阶段\" align=\"center\" prop=\"stageClass\">\n              <template slot-scope=\"scope\">\n                <dict-tag :options=\"dict.type.hw_stage_class\" :value=\"scope.row.stageClass\"/>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"任务内容\" align=\"center\" prop=\"content\" />\n            <el-table-column label=\"任务开始时间\" align=\"center\" prop=\"startTime\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"任务结束时间\" align=\"center\" prop=\"endTime\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"负责人\" align=\"center\" prop=\"manageUserName\"/>\n            <el-table-column label=\"关联事务\" align=\"center\" prop=\"operateWorkName\" />\n            <el-table-column\n              label=\"操作\"\n              fixed=\"right\"\n              :show-overflow-tooltip=\"false\"\n              width=\"160\"\n              class-name=\"small-padding fixed-width\"\n            >\n              <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\n                <el-button size=\"mini\" type=\"text\" @click=\"handleUpdate(scope.row)\">编辑</el-button>\n                <el-button size=\"mini\" type=\"text\" @click=\"handleDelete(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n          />\n        </div>\n      </div>\n    </div>\n\n    <!-- 添加或修改事务管理对话框! -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\n      <el-row>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"HW阶段\" prop=\"stageClass\">\n              <el-select v-model=\"form.stageClass\" clearable placeholder=\"请选择\">\n                <el-option v-for=\"dict in dict.type.hw_stage_class\"\n                           :key=\"dict.value\" :label=\"dict.label\"\n                           :value=\"dict.value\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务名称\" prop=\"taskName\">\n              <el-input v-model=\"form.taskName\" placeholder=\"请输入任务名称\" maxlength=\"20\" show-word-limit/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"任务内容\" prop=\"content\">\n              <el-input type=\"textarea\" v-model=\"form.content\" placeholder=\"请输入任务内容\" maxlength=\"500\" show-word-limit/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务开始时间\" prop=\"startTime\">\n              <el-date-picker type=\"datetime\" placeholder=\"请选择\" v-model=\"form.startTime\" :picker-options=\"startPickerOptions\" style=\"width: 100%;\" :default-value=\"defaultStartTime\"></el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务完成时间\" prop=\"endTime\">\n              <el-date-picker type=\"datetime\" placeholder=\"请选择\" v-model=\"form.endTime\" :picker-options=\"endPickerOptions\" style=\"width: 100%;\" :default-value=\"defaultStartTime\"></el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"责任人\" prop=\"manageUser\">\n              <user-select v-model=\"form.manageUser\"></user-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"关联事务\" prop=\"operateWorkId\">\n              <el-select v-model=\"form.operateWorkId\" clearable filterable placeholder=\"请选择\">\n                <el-option v-for=\"item in operateWorkList\"\n                           :key=\"item.id\" :label=\"item.workName\"\n                           :value=\"item.id\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\" :disabled=\"btnLoading\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listWorkHwTask, getWorkHwTask, delWorkHwTask, addWorkHwTask, updateWorkHwTask,getStageTree } from \"@/api/aqsoc/work-hw/workHwTask\";\nimport {listOperateWork} from \"@/api/operateWork/operateWork\"\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nexport default {\n  name: \"Plan\",\n  components: {UserSelect},\n  dicts: ['hw_stage_class'],\n  props:{\n    workInfo: {\n      type: Object,\n      required: true,\n      default: {}\n    },\n    show: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      dataList: null,\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      // 表单校验\n      rules: {\n        taskName: [\n          { required: true, message: \"任务名称不能为空\", trigger: \"blur\" },\n          { max: 20, message: \"长度不能超过20个字符\", trigger: \"blur\" }\n        ],\n        content: [\n          { required: true, message: \"任务内容不能为空\", trigger: \"blur\" },\n          { max: 500, message: \"长度不能超过500个字符\", trigger: \"blur\" }\n        ],\n        startTime: [\n          { required: true, message: \"任务开始时间不能为空\", trigger: \"blur\" }\n        ],\n        endTime: [\n          { required: true, message: \"任务完成时间不能为空\", trigger: \"blur\" }\n        ],\n        manageUser: [\n          { required: true, message: \"责任人不能为空\", trigger: \"blur\" }\n        ],\n        operateWorkId: [\n          { required: true, message: \"关联事务不能为空\", trigger: \"blur\" }\n        ],\n        stageClass: [\n          { required: true, message: \"HW阶段不能为空\", trigger: \"blur\" }\n        ]\n      },\n      form: {},\n      stageList: [],\n      operateWorkList: [],\n      title: '',\n      btnLoading: false,\n      startPickerOptions: {\n        disabledDate: (time) => {\n          return new Date(time).getTime() < new Date(this.workInfo.hwStart).getTime() || new Date(time).getTime() > new Date(this.form.endTime?this.form.endTime : this.workInfo.hwEnd).getTime();\n        },\n      },\n      endPickerOptions: {\n        disabledDate: (time) => {\n          return new Date(time).getTime() < new Date(this.form.startTime?this.form.startTime : this.workInfo.hwStart).getTime() || new Date(time).getTime() > new Date(this.workInfo.hwEnd).getTime();\n        },\n      },\n      defaultStartTime: new Date(this.workInfo.hwStart),\n    };\n  },\n  watch: {\n  },\n  created() {\n    this.queryParams.workId = this.workInfo.id;\n    this.getStageList();\n    this.getList();\n    this.getOperateWorkList();\n  },\n  methods: {\n    search() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 查询HW事务任务列表 */\n    getList() {\n      this.loading = true;\n      listWorkHwTask(this.queryParams).then(response => {\n        this.dataList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    getStageList(){\n      getStageTree({workId: this.workInfo.id}).then(res => {\n        let arr = res.data;\n        let first = {\n          value: null,\n          label: '全部阶段',\n          sort: 0,\n          count: 0\n        };\n        if(arr && arr.length>0){\n          arr.forEach(item => {\n            first.count += item.count;\n          })\n        }\n        arr.unshift(first);\n        this.stageList = arr;\n      })\n    },\n    getOperateWorkList(){\n      listOperateWork({queryAllData: true}).then(res => {\n        this.operateWorkList = res.rows;\n      })\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加HW事务任务\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getWorkHwTask(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改HW事务任务\";\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除HW事务任务编号为\"' + ids + '\"的数据项？').then(function () {\n        return delWorkHwTask(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {\n      });\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        workId: this.workInfo.id\n      };\n      this.resetForm(\"form\");\n    },\n    back(){\n      this.$emit('update:show',false);\n    },\n    submitForm(){\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          this.btnLoading = true;\n          this.form.workId = this.workInfo.id;\n          let currentForm = {...this.form};\n          if (this.form.id != null) {\n            updateWorkHwTask(currentForm).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            }).finally(() => {\n              this.btnLoading = false;\n            });\n          } else {\n            addWorkHwTask(currentForm).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            }).finally(() => {\n              this.btnLoading = false;\n            });\n          }\n        }\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    tabClick(tab){\n      if(tab.paneName !== '0'){\n        this.queryParams.stageClass = tab.paneName;\n      }else {\n        this.queryParams.stageClass = null;\n      }\n      this.search();\n    },\n    getPickerOptions(){\n      let startTime = this.workInfo.hwStart;\n      let endTime = this.workInfo.hwEnd;\n      return {\n        disabledDate: (time) => {\n          console.log(time)\n          return new Date(time).getTime() < new Date(startTime).getTime() || new Date(time).getTime() > new Date(endTime).getTime();\n        },\n      }\n    },\n  }\n};\n</script>\n<style lang=\"scss\" scoped>\n.main{\n  width: 100%;\n\n  .head-box{\n    background: #fff;\n    flex-shrink: 0;\n    margin-bottom: 10px;\n    padding: 15px 10px 15px;\n    position: relative;\n    display: flex;\n\n    .left{\n      align-content: center;\n      font-size: 16px;\n      font-weight: 700;\n    }\n    .right{\n      flex: 1;\n      text-align: right;\n    }\n\n    .btn2{\n      height: 32px;\n      color: #656C75;\n      font-size: 14px;\n      border: 1px solid #dbdbdb;\n      background: #f2f7f7;\n    }\n  }\n\n  .work-tabs{\n    ::v-deep .el-tabs__item {\n      height: 48px !important;\n      line-height: 48px !important;\n      text-align: left !important;\n      width: 285px !important;\n      padding: 0 10px;\n    }\n    /*设置第一个标签项不使用通用的悬停和选中效果*/\n    ::v-deep .el-tabs__item:not(#tab-search):hover {\n      color: #333 !important;\n      background-color: #f5f5f5 !important;\n    }\n    ::v-deep .el-tabs__item:not(#tab-search).is-active {\n      line-height: 48px;\n      color: #333333;\n      font-weight: bold;\n      background-color: #f5f5f5;\n    }\n    .work-tabs-label {\n      padding: 0 10px;\n    }\n  }\n}\n</style>\n"]}]}